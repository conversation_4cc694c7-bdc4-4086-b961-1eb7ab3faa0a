'use strict';

const Access = require('../../../lib/access');
const Auth = require('../../../components/auth');
const SchemaMap = require('../../../components/schemaMap');
const LeadResponse = require('../../../models/payload/base/lead/responses');
const PageSortFilter = new (require('../../../models/payload/base/pageSortFilter'))();
const LeadsViewKnex =
  require('../../../models/database/datahub/datahub/merchant/leadsView').initialize();
const QueryBuilder = require('../../../components/queryBuilder');
const { convertToUTC } = require('../../../lib/functions');
const TimezoneEnum = require('../../../components/timezoneEnum');

const endpoint = {
  origin: process.env.FUNCTION_ORIGIN,
  resourceEndpoint: 'v1.partner.leads.get',
  type: 'partner-leads',
};
const LeadsMappings = SchemaMap.generateFieldMappings(new LeadResponse());

module.exports.register = (app) => {
  app.route({
    method: 'GET',
    path: '/v1/partner/leads',
    config: {
      logs: { namespace: 'v1-partner-leads', resource: endpoint.type },
      docs: {
        tags: ['{{ partner-leads.tag.title }}', 'partner-leads', 'internal'],
        auth: [
          ['developer-id', 'access-token'],
          ['user-id', 'user-api-key', 'developer-id'],
        ],
        title: '{{ partner-leads.endpoint.list.title }}',
        description: '{{ partner-leads.endpoint.list.description }}',
        responses: {
          200: SchemaMap.response([new LeadResponse()], endpoint),
        },
      },
      auth: Auth.validKeyDirect(),
      filterMappings: LeadsMappings,
      validate: {
        query: SchemaMap.query(PageSortFilter, {
          ...endpoint,
          sortAndFilter: new LeadResponse(),
        }),
      },
    },
    handler: async (request, reply) => {
      const userId = request.auth.credentials.auth['user-id'];

      const { canAccessEndpoint, linkedLocationIds } = await Access.getEndpointAccessList(
        userId,
        endpoint.resourceEndpoint,
      );

      // if (!canAccessEndpoint) {
      //   return reply
      //     .response(`You don't have permission to access this endpoint on this server.`)
      //     .code(403);
      // }

      const baseQuery = LeadsViewKnex.read();

      baseQuery
        .join('agnt_inf', 'lead_status_vw.agent_id', '=', 'agnt_inf.agent_id')
        .where((builder) => {
          builder
            .whereIn('agnt_inf.agent_location_id', linkedLocationIds)
            .orWhereIn('agnt_inf.office_location_id', linkedLocationIds);
        });
      
      const fullQuery = LeadsViewKnex.read(); 

        

      const transformedFilter = request.meta.transformedData.filter || {};
      const transformedOrder = request.meta.transformedData.order || {};

      QueryBuilder.applyFilterAndSort(
        transformedFilter,
        transformedOrder,
        baseQuery,
        LeadsMappings,
      );

      const totalCountQuery = QueryBuilder.countQueryAsync(baseQuery.clone());
      const totalCountQuery2 = QueryBuilder.countQueryAsync(fullQuery.clone());

      

      const { pageSize, pageNumber } = QueryBuilder.applyPaging(request, baseQuery);

      const [totalCountResult, dbRecords, totalCountResult2 ] = await Promise.all([totalCountQuery, baseQuery, totalCountQuery2]);

      console.log('goat totalCountResult2', totalCountResult2)

      const totalCount = totalCountResult;

      const transformedRecords = dbRecords.map((record) => {
        return {
          hubspot_company_id: record.hubspot_id,
          fortis_id: record.fortis_id,
          company_name: record.company_name,
          first_name: record.first_name,
          last_name: record.last_name,
          email: record.email,
          phone: record.phone,
          pipeline: record.pipeline,
          deal_name: record.deal_name,
          deal_stage: record.deal_stage,
          status: record.status,
          fortis_office_id: record.office_id,
          agent_id: record.agent_id,
          agent_location_id: record.agent_location_id,
          office_location_id: record.office_location_id,
          is_inside_agent: record.is_inside_agent,
          inside_agent: record.inside_agent,
          referral_source: record.referral_source,
          volume: record.volume,
          platform: record.platform,
          assigned_ts: record.lead_assigned_date
            ? convertToUTC(record.lead_assigned_date, { timezone: TimezoneEnum.UTC, format: 'X' })
            : null,
          prospecting_ts: record.lead_prospecting_date
            ? convertToUTC(record.lead_prospecting_date, {
                timezone: TimezoneEnum.UTC,
                format: 'X',
              })
            : null,
          qualifying_ts: record.lead_qualifying_date
            ? convertToUTC(record.lead_qualifying_date, { timezone: TimezoneEnum.UTC, format: 'X' })
            : null,
          qualified_ts: record.lead_qualified_date
            ? convertToUTC(record.lead_qualified_date, { timezone: TimezoneEnum.UTC, format: 'X' })
            : null,
          discovery_ts: record.lead_discovery_date
            ? convertToUTC(record.lead_discovery_date, { timezone: TimezoneEnum.UTC, format: 'X' })
            : null,
          presentation_ts: record.lead_demo_presentation_date
            ? convertToUTC(record.lead_demo_presentation_date, {
                timezone: TimezoneEnum.UTC,
                format: 'X',
              })
            : null,
          application_ts: record.lead_application_date
            ? convertToUTC(record.lead_application_date, {
                timezone: TimezoneEnum.UTC,
                format: 'X',
              })
            : null,
          application_submitted_ts: record.lead_application_submitted_date
            ? convertToUTC(record.lead_application_submitted_date, {
                timezone: TimezoneEnum.UTC,
                format: 'X',
              })
            : null,
          lost_opportunity_ts: record.lead_lost_opportunity_date
            ? convertToUTC(record.lead_lost_opportunity_date, {
                timezone: TimezoneEnum.UTC,
                format: 'X',
              })
            : null,
          lost_opportunity_reason: record.lead_lost_opportunity_reason,
          lost_opportunity_explanation: record.lost_opportunity_explanation,
          closed_ts: record.closed_date
            ? convertToUTC(record.closed_date, { timezone: TimezoneEnum.UTC, format: 'X' })
            : null,
          closed_won_reason: record.closed_won_reason,
          other_closed_won_reason: record.other_closed_won_reason,
          status_entry_ts: record.status_entry_date
            ? convertToUTC(record.status_entry_date, { timezone: TimezoneEnum.UTC, format: 'X' })
            : null,
          deal_owner: record.deal_owner,
          opened_ts: record.lead_opened_date
            ? convertToUTC(record.lead_opened_date, { timezone: TimezoneEnum.UTC, format: 'X' })
            : null,
          client_app_id: record.client_app_id,
          website: record.website,
        };
      });

      const pageCount = pageSize > 0 ? Math.ceil(totalCount / pageSize) : 0;

      const responsePayload = {
        list: transformedRecords,
        type: 'leads',
        pagination: {
          total_count: totalCount,
          page_count: pageCount,
          page_number: pageNumber,
          page_size: pageSize,
        },
      };

      if (responsePayload.pagination.page_number > 0) {
        responsePayload.pagination.page_number -= 1;
      }

      return reply
        .response(responsePayload)
        .meta('origin', endpoint.origin)
        .meta('type', endpoint.type)
        .meta('meta', { pagination: responsePayload.pagination })
        .code(200);
    },
  });
};
