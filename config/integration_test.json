{"JWT": {"ISSUER": "local.oneco.fortispay.com", "SECRET": "test123"}, "DB_BACKEND": {"read": {"config": {"debug": false, "client": "mssql", "connection": {"server": "127.0.0.1", "user": "sa", "password": "Password1", "requestTimeout": 600000, "options": {"port": 1434}}, "pool": {"min": 0, "max": 10}}}, "write": {"config": {"debug": false, "client": "mssql", "connection": {"server": "127.0.0.1", "user": "sa", "password": "Password1", "options": {"port": 1434}}, "pool": {"min": 0, "max": 10}}}}, "DB_DATAHUB": {"read": {"config": {"debug": false, "client": "pg", "connection": {"host": "127.0.0.1", "port": 5433, "user": "postgres", "password": "postgres", "pool": {"min": 0, "max": 10}}}}, "write": {"config": {"debug": false, "client": "pg", "connection": {"host": "127.0.0.1", "port": 5433, "user": "postgres", "password": "postgres", "pool": {"min": 0, "max": 10}}}}}, "DB_MYSQL_MAIN": {"read": {"config": {"debug": false, "client": "mysql2", "connection": {"host": "127.0.0.1", "port": 3307, "user": "test", "password": "test", "database": "nonprod"}}, "pool": {"min": 2, "max": 10}}, "write": {"config": {"debug": false, "client": "mysql2", "connection": {"host": "127.0.0.1", "port": 3307, "user": "root", "password": "Password1", "database": "nonprod", "debug": false}}, "pool": {"min": 2, "max": 10}}}, "DB_MYSQL_LOG": {"read": {"config": {"debug": false, "client": "mysql2", "connection": {"host": "127.0.0.1", "port": 3307, "user": "test", "password": "test", "database": "zeamster_log"}}, "pool": {"min": 2, "max": 10}}, "write": {"config": {"debug": false, "client": "mysql2", "connection": {"host": "127.0.0.1", "port": 3307, "user": "test", "password": "test", "database": "zeamster_log", "debug": false}}, "pool": {"min": 2, "max": 10}}}, "INTERNAL_SECRET": "internalSecretTest123", "ONBOARDING_CREDENTIALS": {}, "S3_CLIENT_CONFIGURATION": {"forcePathStyle": true, "region": "us-east-1", "endpoint": "http://127.0.0.1:4566", "credentials": {"accessKeyId": "test", "secretAccessKey": "test"}}, "DYNAMODB": {"region": "us-east-1", "endpoint": "http://127.0.0.1:4566", "credentials": {"accessKeyId": "test", "secretAccessKey": "test"}}, "BIDS": {"FORTIS": {"VISA": "11111111"}}}