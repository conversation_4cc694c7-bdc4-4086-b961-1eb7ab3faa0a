const seedDataBase = require('../../seedDataBase');
const _ = require('lodash');


module.exports = class PartnerLeadsDataSetup extends seedDataBase {
  #datahubDatabase;
  #isSchemaReady = false;

  constructor(datahubDatabase) {
    super();
    this.#datahubDatabase = datahubDatabase;
  }

  async initialize() {
    if (this.#isSchemaReady) {
      return;
    }

    try {
      await this.#datahubDatabase.transaction(async trx => {

       const result = await trx.raw(`
            SELECT pg_get_viewdef('merchant.lead_status_vw', true) as definition;
        `);
        const { definition } = result.rows[0];
        
          await trx.raw('DROP VIEW IF EXISTS merchant.lead_status_vw;');
          await trx.raw('ALTER TABLE hubspot.deal ALTER COLUMN pipeline TYPE VARCHAR(50);');
          await trx.raw(`CREATE VIEW merchant.lead_status_vw AS ${definition}`);
      });
      this.#isSchemaReady = true;

    } catch (error) {
        throw error;
    }
  }

  async _createRecordAsync(seedToUse, data) {
    if (!this.#isSchemaReady) {
        await this.initialize();
    }
    
    const defaultData = {
        hubspot_company_id: `hs_company_${seedToUse}`,
        fortis_id: `FORT-${seedToUse}`,
        company_name: `Test Company ${seedToUse}`,
        first_name: `John${seedToUse}`,
        last_name: `Doe${seedToUse}`,
        email: `test${seedToUse}@example.com`,
        phone: `555-000-${seedToUse.toString().padStart(4, '0')}`,
        pipeline: this.getRandomFromArray(['chiro_pipeline', 'direct_sales_pipeline', 'erp_pipeline', 'lodging_pipeline']),
        deal_name: `Deal for ${seedToUse}`,
        deal_stage: 'Prospecting',
        application_status: 'Open',
        referral_source: 'Website Referral',
        monthly_volume: '25000',
        client_app_id: `APP-${seedToUse}`,
        deal_owner: `owner_${seedToUse}`,
        closed_won_reason: 'Best Price',
        website: `https://company${seedToUse}.com`,
        create_dt: this.generateRandomDate(),
        close_dt: this.generateRandomDate(),
        assigned_dt: this.generateRandomDate(),
        prospecting_dt: this.generateRandomDate(),
        qualifying_dt: this.generateRandomDate(),
        qualified_dt: this.generateRandomDate(),
        discovery_dt: this.generateRandomDate(),
        presentation_dt: this.generateRandomDate(),
        application_dt: this.generateRandomDate(),
        application_submitted_dt: this.generateRandomDate(),
        closed_dt_lost: this.getRandomFromBool() ? this.generateRandomDate() : null,
      };
  
      const finalData = _.merge({}, defaultData, data);
      const companyId = finalData.hubspot_company_id;
      const dealId = `hs_deal_${seedToUse}`;
      const contactId = `hs_contact_${seedToUse}`;
  
      const dealDateColumns = {};
      const pipelinePrefix = finalData.pipeline.replace('_pipeline', '');
  
      dealDateColumns[`${pipelinePrefix}_pipeline_assigned_entered_dt`] = finalData.assigned_dt;
      dealDateColumns[`${pipelinePrefix}_pipeline_prospecting_entered_dt`] = finalData.prospecting_dt;
      dealDateColumns[`${pipelinePrefix}_pipeline_qualifying_entered_dt`] = finalData.qualifying_dt;
      dealDateColumns[`${pipelinePrefix}_pipeline_qualifying_exited_dt`] = finalData.qualified_dt;
      dealDateColumns[`${pipelinePrefix}_pipeline_discovery_entered_dt`] = finalData.discovery_dt;
      dealDateColumns[`${pipelinePrefix}_pipeline_demo_entered_dt`] = finalData.presentation_dt;
      dealDateColumns[`${pipelinePrefix}_pipeline_application_process_entered_dt`] = finalData.application_dt;
      dealDateColumns[`${pipelinePrefix}_pipeline_application_submitted_entered_dt`] = finalData.application_submitted_dt;
      dealDateColumns[`${pipelinePrefix}_pipeline_closed_lost_entered_dt`] = finalData.closed_dt_lost;
      
      try {
        await this.#datahubDatabase.transaction(async trx => {
            await trx.withSchema('hubspot').table('company').insert({
              company_id: companyId,
              corp_nm: finalData.company_name,
              fortis_id: finalData.fortis_id,
              website: finalData.website,
              hubspot_owner_id: finalData.deal_owner,
            });
  
            await trx.withSchema('hubspot').table('contact').insert({
              contact_id: contactId,
              first_nm: finalData.first_name,
              last_nm: finalData.last_name,
              email: finalData.email,
              phone: finalData.phone,
              create_dt: finalData.create_dt,
            });
  
            await trx.withSchema('hubspot').table('deal').insert({
              deal_id: dealId,
              pipeline: finalData.pipeline,
              dealname: finalData.deal_name,
              dealstage: finalData.deal_stage,
              application_status: finalData.application_status,
              referring_agent_name: finalData.referral_source,
              monthly_volume: finalData.monthly_volume,
              client_app_id: finalData.client_app_id,
              hubspot_owner_id: finalData.deal_owner,
              create_dt: finalData.create_dt,
              close_dt: finalData.close_dt,
              closed_won_reason: finalData.closed_won_reason,
              ...dealDateColumns
            });
  
            await trx.withSchema('hubspot').table('company_contact').insert({
              company_id: companyId,
              contact_id: contactId,
            });
            
            await trx.withSchema('hubspot').table('company_deal').insert({
              company_id: companyId,
              deal_id: dealId,
            });
        });
      } catch (error) {
        throw error;
      }
  }

  async createRecordAsync(seedToUse, data = {}) {
    return this._createRecordAsync(seedToUse, data);
  }

  async createLeadWithSpecificAgent(seedToUse, agentId, agentLocationId, officeLocationId = null) {
    return this._createRecordAsync(seedToUse, {
      agent_id: agentId.toString(),
    });
  }

  async createLeadWithTimestamps(seedToUse, timestamps = {}) {
    return this._createRecordAsync(seedToUse, timestamps);
  }

  async createLeadWithStatus(seedToUse, status, dealStage = null) {
    return this._createRecordAsync(seedToUse, {
      application_status: status,
      deal_stage: dealStage || 'Qualifying'
    });
  }
  
  async createMultipleLeads(startSeed, count, commonData = {}) {
      const promises = [];
      for (let i = 0; i < count; i++) {
        promises.push(this._createRecordAsync(startSeed + i, {
          ...commonData
        }));
      }
      return Promise.all(promises);
  }

  async createLeadWithSpecificData(seedToUse, specificData) {
    return this._createRecordAsync(seedToUse, specificData);
  }
};