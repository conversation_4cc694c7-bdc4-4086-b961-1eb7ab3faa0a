'use strict';
const { ListEndpointBaseTests } = require('../../listEndpointBaseTests');
const PartnerLeadsDataSetup = require('../../../data/datahub/merchant/partnerLeadsDataSetup');

class PartnerLeadsListTest extends ListEndpointBaseTests {
  #partnerLeadsDataSetup;

  constructor() {
    super('../../routes/v1/partner-leads/list');
  }

  async init() {
    // Arrange
    console.log('Initializing PartnerLeadsListTest...');
    console.log('datahubDatabase available:', !!this.testContext.datahubDatabase);

    this.#partnerLeadsDataSetup = new PartnerLeadsDataSetup(
      this.testContext.datahubDatabase,
    );

    console.log('PartnerLeadsDataSetup initialized:', !!this.#partnerLeadsDataSetup);
  }

  testsToSkip() {
    return [this.TestNames.PagingResultsDataForOrderingChildRecords];
  }

  async seedDataForCheckingResponsesMatchAsync() {
    // Arrange - Create test data for response validation
    const userLocationId = this.testContext.userLocationIds[0];
    
    await this.#partnerLeadsDataSetup.createLeadWithSpecificAgent(
      1001,
      'AGT-001',
      userLocationId,
      userLocationId
    );
    
    await this.#partnerLeadsDataSetup.createLeadWithSpecificAgent(
      1002,
      'AGT-002', 
      userLocationId,
      userLocationId
    );
  }

  filterByFieldsToSkip() {
    // Skip complex fields that can't be easily filtered
    return [
      'assigned_ts', 'prospecting_ts', 'qualifying_ts', 'qualified_ts',
      'discovery_ts', 'presentation_ts', 'application_ts', 'application_submitted_ts',
      'lost_opportunity_ts', 'closed_ts', 'status_entry_ts', 'opened_ts',
      'agent_location_id', 'office_location_id', 'is_inside_agent'
    ];
  }

  orderByFieldsToSkip() {
    // Skip complex fields that can't be easily ordered
    return [
      'agent_location_id', 'office_location_id', 'is_inside_agent'
    ];
  }

  async seedDataForFilterByAsync() {
    // Arrange - Create diverse test data for filtering and ordering
    console.log('Starting seedDataForFilterByAsync...');
    console.log('Setup available:', !!this.#partnerLeadsDataSetup);

    if (!this.#partnerLeadsDataSetup) {
      throw new Error('PartnerLeadsDataSetup not initialized');
    }

    const userLocationId = this.testContext.userLocationIds[0];
    console.log('Using userLocationId:', userLocationId);

    // Lead 1: Alpha company with specific characteristics
    console.log('Creating Alpha company lead...');
    await this.#partnerLeadsDataSetup.createLeadWithSpecificData(2001, {
      agent_id: 'AGT-FILTER-001',
      agent_location_id: userLocationId,
      office_location_id: userLocationId,
      company_name: 'Alpha Corporation',
      first_name: 'Alice',
      last_name: 'Anderson',
      email: '<EMAIL>',
      phone: '************',
      pipeline: 'Sales Pipeline A',
      deal_name: 'Alpha Deal',
      deal_stage: 'Prospecting',
      status: 'Open',
      fortis_office_id: 'OFF-ALPHA',
      referral_source: 'Website',
      volume: '50000',
      platform: 'HubSpot',
      deal_owner: 'Agent Alpha'
    });

    // Lead 2: Beta company with different characteristics
    await this.#partnerLeadsDataSetup.createLeadWithSpecificData(2002, {
      agent_id: 'AGT-FILTER-002',
      agent_location_id: userLocationId,
      office_location_id: userLocationId,
      company_name: 'Beta Industries',
      first_name: 'Bob',
      last_name: 'Brown',
      email: '<EMAIL>',
      phone: '************',
      pipeline: 'Sales Pipeline B',
      deal_name: 'Beta Deal',
      deal_stage: 'Qualifying',
      status: 'In Progress',
      fortis_office_id: 'OFF-BETA',
      referral_source: 'Referral',
      volume: '100000',
      platform: 'Salesforce',
      deal_owner: 'Agent Beta'
    });

    // Lead 3: Gamma company for additional variety
    await this.#partnerLeadsDataSetup.createLeadWithSpecificData(2003, {
      agent_id: 'AGT-FILTER-003',
      agent_location_id: userLocationId,
      office_location_id: userLocationId,
      company_name: 'Gamma Solutions',
      first_name: 'Charlie',
      last_name: 'Clark',
      email: '<EMAIL>',
      phone: '************',
      pipeline: 'Sales Pipeline A',
      deal_name: 'Gamma Deal',
      deal_stage: 'Qualified',
      status: 'Closed',
      fortis_office_id: 'OFF-GAMMA',
      referral_source: 'Cold Call',
      volume: '25000',
      platform: 'Pipedrive',
      deal_owner: 'Agent Gamma'
    });

    // Lead 4: Delta company for edge cases
    await this.#partnerLeadsDataSetup.createLeadWithSpecificData(2004, {
      agent_id: 'AGT-FILTER-004',
      agent_location_id: userLocationId,
      office_location_id: userLocationId,
      company_name: 'Delta Enterprises',
      first_name: 'Diana',
      last_name: 'Davis',
      email: '<EMAIL>',
      phone: '************',
      pipeline: 'Sales Pipeline C',
      deal_name: 'Delta Deal',
      deal_stage: 'Discovery',
      status: 'Lost',
      fortis_office_id: 'OFF-DELTA',
      referral_source: 'Email Campaign',
      volume: '250000',
      platform: 'Zoho',
      deal_owner: 'Agent Delta'
    });

    // Lead 5: Create a lead for a different location (should not appear in results)
    await this.#partnerLeadsDataSetup.createLeadWithSpecificData(2005, {
      agent_id: 'AGT-OTHER-001',
      agent_location_id: 'other-location-id',
      office_location_id: 'other-location-id',
      company_name: 'Other Company',
      first_name: 'Other',
      last_name: 'User',
      email: '<EMAIL>',
      phone: '************',
      pipeline: 'Other Pipeline',
      deal_name: 'Other Deal',
      deal_stage: 'Prospecting',
      status: 'Open',
      fortis_office_id: 'OFF-OTHER',
      referral_source: 'Website',
      volume: '10000',
      platform: 'HubSpot',
      deal_owner: 'Other Agent'
    });
  }

  runTests() {
    super.runTests();

    describe('GET /v1/partner/leads (Specific Logic)', () => {
      this.truncateDatabaseAfterEach();

      beforeEach(async () => {
        // Initialize the data setup if not already done
        if (!this.#partnerLeadsDataSetup) {
          console.log('Initializing PartnerLeadsDataSetup in beforeEach...');
          this.#partnerLeadsDataSetup = new PartnerLeadsDataSetup(
            this.testContext.datahubDatabase,
          );
          console.log('PartnerLeadsDataSetup initialized successfully');
        }
      });

      it('should return empty list when no leads exist for the user location', async () => {
        // Arrange: No data setup needed

        // Act
        const response = await this.sendRequestAsync();

        // Assert
        expect(response.statusCode).toBe(200);
        const parsedBody = JSON.parse(response.body);
        expect(parsedBody.list).toEqual([]);
        expect(parsedBody.type).toBe('leads');
        expect(parsedBody.pagination.total_count).toBe(0);
      }, 30000);

      it.only('should return list of partner leads for user location, excluding other locations', async () => {
        // Arrange
        const userLocationId = this.testContext.userLocationIds[0];
        
        await this.#partnerLeadsDataSetup.createLeadWithSpecificAgent(
          3001,
          'AGT-TEST-001',
          userLocationId,
          userLocationId
        );
        
        await this.#partnerLeadsDataSetup.createLeadWithSpecificAgent(
          3002,
          'AGT-TEST-002',
          userLocationId,
          userLocationId
        );
        
        // Create lead for different location (should not appear)
        await this.#partnerLeadsDataSetup.createLeadWithSpecificAgent(
          3003,
          'AGT-OTHER-001',
          'other-location-id',
          'other-location-id'
        );

        // Act
        const response = await this.sendRequestAsync();

        // Assert
        expect(response.statusCode).toBe(200);
        const parsedBody = JSON.parse(response.body);
        expect(parsedBody.list.length).toBe(2);
        expect(parsedBody.pagination.total_count).toBe(2);
        
        // Verify only leads from user's location are returned
        const returnedCompanyNames = parsedBody.list.map((lead) => lead.company_name);
        expect(returnedCompanyNames).toContain('Test Company 3001');
        expect(returnedCompanyNames).toContain('Test Company 3002');
        expect(returnedCompanyNames).not.toContain('Test Company 3003');
      }, 30000);

      it('should support filtering by company_name', async () => {
        // Arrange
        const userLocationId = this.testContext.userLocationIds[0];
        
        await this.#partnerLeadsDataSetup.createLeadWithSpecificData(4001, {
          agent_id: 'AGT-FILTER-001',
          agent_location_id: userLocationId,
          office_location_id: userLocationId,
          company_name: 'Acme Corporation'
        });
        
        await this.#partnerLeadsDataSetup.createLeadWithSpecificData(4002, {
          agent_id: 'AGT-FILTER-002',
          agent_location_id: userLocationId,
          office_location_id: userLocationId,
          company_name: 'Beta Industries'
        });

        // Act
        const response = await this.sendRequestAsync({
          filter: JSON.stringify({ company_name: 'Acme Corporation' })
        });

        // Assert
        expect(response.statusCode).toBe(200);
        const parsedBody = JSON.parse(response.body);
        expect(parsedBody.list.length).toBe(1);
        expect(parsedBody.list[0].company_name).toBe('Acme Corporation');
      }, 30000);

      it('should support sorting by company_name', async () => {
        // Arrange
        const userLocationId = this.testContext.userLocationIds[0];
        
        await this.#partnerLeadsDataSetup.createLeadWithSpecificData(5001, {
          agent_id: 'AGT-SORT-001',
          agent_location_id: userLocationId,
          office_location_id: userLocationId,
          company_name: 'Zebra Corp'
        });
        
        await this.#partnerLeadsDataSetup.createLeadWithSpecificData(5002, {
          agent_id: 'AGT-SORT-002',
          agent_location_id: userLocationId,
          office_location_id: userLocationId,
          company_name: 'Alpha Corp'
        });

        // Act
        const response = await this.sendRequestAsync({
          sort: JSON.stringify([{ field: 'company_name', direction: 'asc' }])
        });

        // Assert
        expect(response.statusCode).toBe(200);
        const parsedBody = JSON.parse(response.body);
        expect(parsedBody.list.length).toBe(2);
        expect(parsedBody.list[0].company_name).toBe('Alpha Corp');
        expect(parsedBody.list[1].company_name).toBe('Zebra Corp');
      }, 30000);
    });
  }
}

new PartnerLeadsListTest().runTests();
